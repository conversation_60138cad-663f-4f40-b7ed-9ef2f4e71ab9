// MCP Tools Tab - Displays and manages available MCP tools
import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { 
  Tool, 
  Play, 
  Code, 
  FileText,
  GitBranch,
  GitPullRequest,
  Search,
  FolderPlus,
  AlertTriangle
} from 'lucide-react';

interface MCPTool {
  name: string;
  description: string;
  inputSchema?: any;
}

interface MCPTabToolsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  serverId: string;
  tools: MCPTool[];
}

export const MCPTabTools: React.FC<MCPTabToolsProps> = ({
  open,
  onOpenChange,
  serverId,
  tools
}) => {
  const [selectedTool, setSelectedTool] = useState<MCPTool | null>(null);

  const getToolIcon = (toolName: string) => {
    const name = toolName.toLowerCase();
    
    if (name.includes('repository') || name.includes('repo')) {
      return <FolderPlus className="h-4 w-4" />;
    }
    if (name.includes('issue')) {
      return <AlertTriangle className="h-4 w-4" />;
    }
    if (name.includes('pull') || name.includes('pr')) {
      return <GitPullRequest className="h-4 w-4" />;
    }
    if (name.includes('branch')) {
      return <GitBranch className="h-4 w-4" />;
    }
    if (name.includes('search')) {
      return <Search className="h-4 w-4" />;
    }
    if (name.includes('file')) {
      return <FileText className="h-4 w-4" />;
    }
    if (name.includes('code')) {
      return <Code className="h-4 w-4" />;
    }
    
    return <Tool className="h-4 w-4" />;
  };

  const getToolCategory = (toolName: string) => {
    const name = toolName.toLowerCase();
    
    if (name.includes('repository') || name.includes('repo')) {
      return 'Repository';
    }
    if (name.includes('issue')) {
      return 'Issues';
    }
    if (name.includes('pull') || name.includes('pr')) {
      return 'Pull Requests';
    }
    if (name.includes('branch')) {
      return 'Branches';
    }
    if (name.includes('search')) {
      return 'Search';
    }
    if (name.includes('file')) {
      return 'Files';
    }
    
    return 'General';
  };

  const groupedTools = tools.reduce((groups, tool) => {
    const category = getToolCategory(tool.name);
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(tool);
    return groups;
  }, {} as Record<string, MCPTool[]>);

  const handleToolExecute = async (tool: MCPTool) => {
    try {
      // For now, just show that the tool would be executed
      // In a real implementation, this would open a parameter input dialog
      console.log('Would execute tool:', tool.name);
      
      // You could implement parameter collection and execution here
      // const result = await window.api.mcpExecuteTool({
      //   serverId,
      //   toolName: tool.name,
      //   parameters: {}
      // });
      
    } catch (error) {
      console.error('Failed to execute tool:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Tool className="h-5 w-5" />
            <span>Available Tools - {serverId}</span>
          </DialogTitle>
          <DialogDescription>
            {tools.length} tools available for GitHub operations
          </DialogDescription>
        </DialogHeader>

        <div className="flex space-x-4 h-96">
          {/* Tools List */}
          <div className="flex-1">
            <ScrollArea className="h-full">
              <div className="space-y-4">
                {Object.entries(groupedTools).map(([category, categoryTools]) => (
                  <div key={category}>
                    <h3 className="text-sm font-semibold text-gray-700 mb-2">
                      {category}
                    </h3>
                    <div className="space-y-2">
                      {categoryTools.map((tool) => (
                        <div
                          key={tool.name}
                          className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                            selectedTool?.name === tool.name
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                          onClick={() => setSelectedTool(tool)}
                        >
                          <div className="flex items-start space-x-3">
                            <div className="text-gray-500 mt-0.5">
                              {getToolIcon(tool.name)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2">
                                <h4 className="text-sm font-medium text-gray-900 truncate">
                                  {tool.name}
                                </h4>
                                <Badge variant="secondary" className="text-xs">
                                  {getToolCategory(tool.name)}
                                </Badge>
                              </div>
                              <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                                {tool.description}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Tool Details */}
          {selectedTool && (
            <>
              <Separator orientation="vertical" />
              <div className="flex-1">
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      {getToolIcon(selectedTool.name)}
                      <h3 className="text-lg font-semibold">{selectedTool.name}</h3>
                    </div>
                    <Badge variant="outline">
                      {getToolCategory(selectedTool.name)}
                    </Badge>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-1">Description</h4>
                    <p className="text-sm text-gray-600">{selectedTool.description}</p>
                  </div>

                  {selectedTool.inputSchema && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-1">Parameters</h4>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <pre className="text-xs text-gray-600 overflow-auto">
                          {JSON.stringify(selectedTool.inputSchema, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}

                  <div className="pt-4">
                    <Button
                      onClick={() => handleToolExecute(selectedTool)}
                      className="w-full"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Execute Tool
                    </Button>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {!selectedTool && (
          <div className="text-center py-8 text-gray-500">
            <Tool className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Select a tool to view details</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
