// GitHub Service Card - Main UI component for GitHub MCP integration
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { 
  Github, 
  Settings, 
  Play, 
  Square, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  Tool,
  User,
  RefreshCw
} from 'lucide-react';
import { GitHubPATDialog } from './GitHubPATDialog';
import { ServerConnectionStatus } from './ServerConnectionStatus';
import { MCPTabTools } from './MCPTabTools';
import { PATInstructionsModal } from './PATInstructionsModal';

interface GitHubUser {
  login: string;
  name: string;
  avatar_url: string;
}

interface MCPTool {
  name: string;
  description: string;
}

interface MCPServerStatus {
  id: string;
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  lastConnected?: Date;
  error?: string;
  tools?: MCPTool[];
}

export const GitHubServiceCard: React.FC = () => {
  const [serverStatus, setServerStatus] = useState<MCPServerStatus>({
    id: 'github',
    status: 'disconnected'
  });
  const [githubUser, setGithubUser] = useState<GitHubUser | null>(null);
  const [showPATDialog, setShowPATDialog] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);
  const [showTools, setShowTools] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Load initial data
  useEffect(() => {
    loadServerStatus();
    loadGitHubUser();
  }, []);

  // Set up status polling
  useEffect(() => {
    const interval = setInterval(() => {
      if (serverStatus.status === 'connecting' || serverStatus.status === 'connected') {
        loadServerStatus();
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [serverStatus.status]);

  const loadServerStatus = async () => {
    try {
      const status = await window.api.mcpGetServerStatus('github');
      setServerStatus(status);
    } catch (error) {
      console.error('Failed to load server status:', error);
    }
  };

  const loadGitHubUser = async () => {
    try {
      const authStatus = await window.api.githubMCPGetAuthStatus();
      if (authStatus.authenticated && authStatus.username) {
        // Get full user info
        const user = await window.api.githubMCPGetUser();
        setGithubUser(user);
      }
    } catch (error) {
      console.error('Failed to load GitHub user:', error);
    }
  };

  const handleConnect = async () => {
    setShowPATDialog(true);
  };

  const handleDisconnect = async () => {
    try {
      setIsLoading(true);
      await window.api.mcpDisconnectServer('github');
      setServerStatus(prev => ({ ...prev, status: 'disconnected' }));
      setGithubUser(null);
    } catch (error) {
      console.error('Failed to disconnect:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePATSubmit = async (pat: string) => {
    try {
      setIsLoading(true);
      setShowPATDialog(false);
      
      // Update status to connecting
      setServerStatus(prev => ({ ...prev, status: 'connecting' }));
      
      // Connect with PAT
      await window.api.mcpConnectServer('github', { pat });
      
      // Reload user info
      await loadGitHubUser();
      
    } catch (error) {
      console.error('Failed to connect with PAT:', error);
      setServerStatus(prev => ({ 
        ...prev, 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Connection failed'
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setIsLoading(true);
      await loadServerStatus();
      await loadGitHubUser();
    } catch (error) {
      console.error('Failed to refresh:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = () => {
    switch (serverStatus.status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'connecting':
        return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Square className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (serverStatus.status) {
      case 'connected':
        return 'Connected';
      case 'connecting':
        return 'Connecting...';
      case 'error':
        return 'Error';
      default:
        return 'Disconnected';
    }
  };

  const getStatusColor = () => {
    switch (serverStatus.status) {
      case 'connected':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'connecting':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <>
      <Card className="w-full max-w-md">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gray-900 rounded-lg">
                <Github className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg">GitHub MCP Server</CardTitle>
                <CardDescription>
                  Repository management and automation
                </CardDescription>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Connection Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span className="text-sm font-medium">{getStatusText()}</span>
            </div>
            <Badge className={getStatusColor()}>
              {serverStatus.status}
            </Badge>
          </div>

          {/* Error Message */}
          {serverStatus.status === 'error' && serverStatus.error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start space-x-2">
                <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-red-700">{serverStatus.error}</p>
              </div>
            </div>
          )}

          {/* User Info */}
          {githubUser && serverStatus.status === 'connected' && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <img
                src={githubUser.avatar_url}
                alt={githubUser.name}
                className="h-8 w-8 rounded-full"
              />
              <div>
                <p className="text-sm font-medium">{githubUser.name}</p>
                <p className="text-xs text-gray-500">@{githubUser.login}</p>
              </div>
            </div>
          )}

          {/* Tools Info */}
          {serverStatus.status === 'connected' && serverStatus.tools && (
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <Tool className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium text-blue-700">
                  {serverStatus.tools.length} tools available
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowTools(true)}
                className="text-blue-600 hover:text-blue-700"
              >
                View Tools
              </Button>
            </div>
          )}

          <Separator />

          {/* Action Buttons */}
          <div className="flex space-x-2">
            {serverStatus.status === 'disconnected' || serverStatus.status === 'error' ? (
              <>
                <Button
                  onClick={handleConnect}
                  disabled={isLoading}
                  className="flex-1"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Connect
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowInstructions(true)}
                  disabled={isLoading}
                >
                  <Settings className="h-4 w-4" />
                </Button>
              </>
            ) : (
              <Button
                variant="outline"
                onClick={handleDisconnect}
                disabled={isLoading || serverStatus.status === 'connecting'}
                className="flex-1"
              >
                <Square className="h-4 w-4 mr-2" />
                Disconnect
              </Button>
            )}
          </div>

          {/* Last Connected */}
          {serverStatus.lastConnected && (
            <p className="text-xs text-gray-500 text-center">
              Last connected: {serverStatus.lastConnected.toLocaleString()}
            </p>
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <GitHubPATDialog
        open={showPATDialog}
        onOpenChange={setShowPATDialog}
        onSubmit={handlePATSubmit}
      />

      <PATInstructionsModal
        open={showInstructions}
        onOpenChange={setShowInstructions}
      />

      {showTools && serverStatus.tools && (
        <MCPTabTools
          open={showTools}
          onOpenChange={setShowTools}
          serverId="github"
          tools={serverStatus.tools}
        />
      )}
    </>
  );
};
