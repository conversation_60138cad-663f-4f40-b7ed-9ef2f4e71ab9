
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { TitleBar } from "@/components/TitleBar";

const Index = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [token, setToken] = useState<string | null>(null);
  const [loginConfirmed, setLoginConfirmed] = useState(false);
  const navigate = useNavigate();


  useEffect(() => {

    // 1. Listen for deep link login success
    window.api.onAuthSuccess((token) => {
      console.log("Auth token received via web app:", token);
      setToken(token);
      setLoginConfirmed(true);
      setIsLoading(false);
      navigate("/onboarding");
    });

    // 2. Optionally restore token just for session continuity
    window.api.getAuthToken().then((storedToken) => {
      if (storedToken) {
        console.log("Token restored from keytar");
        setToken(storedToken);
      }
    });

    return () => {
      window.api.removeAuthListeners();
    };
  }, []);


  const handleOAuthLogin = async () => {
    setIsLoading(true);
    // Simulate OAuth process - opens browser to Alpine OAuth
    // const session = await window.api.createLoginSession(
    //     "desktop",
    //     "thealpinecode.alpineintellect://auth-callback"
    //   );

    // console.log("Session created:", session);
    const WEB_APP_URL = `${import.meta.env.VITE_CONSOLE_URL}/auth/welcome-login?redirectURI=thealpinecode.alpineintellect://auth-callback&source=desktop`;
    // const WEB_APP_URL = `http://localhost:3000/auth/welcome-login?redirectURI=thealpinecode.alpineintellect://auth-callback&source=desktop`;
    // const WEB_APP_URL = `https://app.thealpinecode.com/auth/welcome-login?session=${session}&source=desktop`;
    console.log("Opening browser to:", WEB_APP_URL);

    // Open browser to actual SSO login
    if (window.api?.openExternal) {
      window.api.openExternal(WEB_APP_URL);
    } else {
      console.error("openExternal not available");
      setIsLoading(false);
      return;
    }

    // The rest will be handled when the token is received via IPC
    // So don't stop loading here — keep showing loader until main.ts sends the auth token

    // Simulated fallback in case token not handled yet (OPTIONAL for dev testing)
    // setTimeout(() => {
    //   console.log("Timeout fallback - token not received.");
    //   setIsLoading(false);
    // }, 10000);
  };


  return (
    <div className="min-h-screen bg-slate-900 flex flex-col">
      {/* Title Bar */}
      <TitleBar title="Alpine Intellect - Sign In" />
      
      {/* Header */}
      <div className="p-6">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">AI</span>
          </div>
          <h1 className="text-white text-xl font-semibold">Alpine Intellect</h1>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center px-6">
        <Card className="w-full max-w-md bg-slate-800 border-slate-700">
          <CardHeader className="text-center">
            <CardTitle className="text-white text-2xl">Welcome</CardTitle>
            <CardDescription className="text-slate-400">
              Sign in to your Alpine Intellect account to get started
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center space-y-4">
              <div className="text-slate-300 text-sm">
                Connect your Alpine Intellect account to access IDE integrations and AI-powered development tools.
              </div>
              
              <Button 
                onClick={handleOAuthLogin}
                className="w-full bg-blue-600 hover:bg-blue-700 h-12 text-base"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Authenticating...</span>
                  </div>
                ) : (
                  "Sign in with Alpine"
                )}
              </Button>
            </div>
            
            {isLoading && (
              <div className="text-center text-slate-400 text-sm space-y-2">
                <div>Opening browser for secure authentication...</div>
                <div className="text-xs text-slate-500">
                  Complete the login in your browser to continue
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Footer */}
      <div className="p-6 text-center">
        <a href="#" className="text-slate-400 hover:text-blue-400 text-sm">
          Need help? Contact Support
        </a>
      </div>
    </div>
  );
};

export default Index;
