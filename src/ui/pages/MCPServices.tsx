// MCP Services Page - Main page for managing MCP server connections
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Separator } from '../components/ui/separator';
import { Alert, AlertDescription } from '../components/ui/alert';
import { 
  Server, 
  RefreshCw, 
  Settings, 
  Info,
  AlertCircle,
  CheckCircle,
  Activity
} from 'lucide-react';
import { GitHubServiceCard } from '../components/mcp/GitHubServiceCard';

interface MCPServerStatus {
  id: string;
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  lastConnected?: Date;
  error?: string;
  tools?: Array<{ name: string; description: string }>;
}

export const MCPServices: React.FC = () => {
  const [servers, setServers] = useState<MCPServerStatus[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Load server statuses on mount
  useEffect(() => {
    loadServers();
    
    // Set up event listeners for real-time updates
    window.api.onMCPServerStatusChanged((status: MCPServerStatus) => {
      setServers(prev => 
        prev.map(server => 
          server.id === status.id ? status : server
        )
      );
    });

    window.api.onMCPServerError((data: { serverId: string; error: any }) => {
      console.error(`MCP Server ${data.serverId} error:`, data.error);
    });

    window.api.onMCPToolsUpdated((data: { serverId: string; tools: any[] }) => {
      setServers(prev =>
        prev.map(server =>
          server.id === data.serverId 
            ? { ...server, tools: data.tools }
            : server
        )
      );
    });

    // Cleanup listeners on unmount
    return () => {
      window.api.removeMCPListeners();
    };
  }, []);

  const loadServers = async () => {
    try {
      setIsLoading(true);
      const serverStatuses = await window.api.mcpGetServers();
      setServers(serverStatuses);
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Failed to load MCP servers:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    await loadServers();
  };

  const getOverallStatus = () => {
    if (servers.length === 0) return 'no-servers';
    
    const connectedCount = servers.filter(s => s.status === 'connected').length;
    const errorCount = servers.filter(s => s.status === 'error').length;
    const connectingCount = servers.filter(s => s.status === 'connecting').length;
    
    if (connectedCount === servers.length) return 'all-connected';
    if (errorCount > 0) return 'has-errors';
    if (connectingCount > 0) return 'connecting';
    return 'disconnected';
  };

  const getStatusSummary = () => {
    const connectedCount = servers.filter(s => s.status === 'connected').length;
    const totalCount = servers.length;
    const totalTools = servers.reduce((sum, server) => sum + (server.tools?.length || 0), 0);
    
    return {
      connectedCount,
      totalCount,
      totalTools
    };
  };

  const overallStatus = getOverallStatus();
  const summary = getStatusSummary();

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center space-x-3">
            <Server className="h-8 w-8" />
            <span>MCP Services</span>
          </h1>
          <p className="text-gray-600 mt-1">
            Manage Model Context Protocol server connections
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={isLoading}
          variant="outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>System Overview</span>
          </CardTitle>
          <CardDescription>
            Current status of all MCP services
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Connection Status */}
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-center space-x-2 mb-2">
                {overallStatus === 'all-connected' ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : overallStatus === 'has-errors' ? (
                  <AlertCircle className="h-5 w-5 text-red-500" />
                ) : (
                  <Server className="h-5 w-5 text-gray-500" />
                )}
                <span className="font-semibold">Connections</span>
              </div>
              <p className="text-2xl font-bold">
                {summary.connectedCount}/{summary.totalCount}
              </p>
              <p className="text-sm text-gray-600">servers connected</p>
            </div>

            {/* Available Tools */}
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <Settings className="h-5 w-5 text-blue-500" />
                <span className="font-semibold">Tools</span>
              </div>
              <p className="text-2xl font-bold">{summary.totalTools}</p>
              <p className="text-sm text-gray-600">tools available</p>
            </div>

            {/* Last Updated */}
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <RefreshCw className="h-5 w-5 text-purple-500" />
                <span className="font-semibold">Last Updated</span>
              </div>
              <p className="text-sm font-medium">
                {lastRefresh.toLocaleTimeString()}
              </p>
              <p className="text-sm text-gray-600">
                {lastRefresh.toLocaleDateString()}
              </p>
            </div>
          </div>

          {/* Status Alerts */}
          {overallStatus === 'has-errors' && (
            <Alert className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                One or more MCP servers have errors. Check individual server status below.
              </AlertDescription>
            </Alert>
          )}

          {overallStatus === 'no-servers' && (
            <Alert className="mt-4">
              <Info className="h-4 w-4" />
              <AlertDescription>
                No MCP servers are configured. Add a server connection to get started.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Separator />

      {/* Available Services */}
      <div>
        <h2 className="text-2xl font-semibold mb-4">Available Services</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* GitHub MCP Service */}
          <GitHubServiceCard />

          {/* Future services can be added here */}
          <Card className="opacity-50">
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <div className="p-2 bg-blue-500 rounded-lg">
                  <Server className="h-6 w-6 text-white" />
                </div>
                <span>Figma MCP Server</span>
              </CardTitle>
              <CardDescription>
                Design file management and automation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Badge variant="secondary">Coming Soon</Badge>
                <p className="text-sm text-gray-500 mt-2">
                  Figma integration will be available in a future release
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="opacity-50">
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <div className="p-2 bg-green-500 rounded-lg">
                  <Server className="h-6 w-6 text-white" />
                </div>
                <span>Custom MCP Server</span>
              </CardTitle>
              <CardDescription>
                Connect your own MCP server
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Badge variant="secondary">Coming Soon</Badge>
                <p className="text-sm text-gray-500 mt-2">
                  Custom server support will be available in a future release
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Info className="h-5 w-5" />
            <span>About MCP Services</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm text-gray-600">
            <p>
              Model Context Protocol (MCP) services provide AI agents with access to external tools and data sources.
              Each service offers specific capabilities that can be used by Alpine Intellect's AI assistants.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">GitHub MCP Server</h4>
                <ul className="space-y-1 text-xs">
                  <li>• Create and manage repositories</li>
                  <li>• Handle issues and pull requests</li>
                  <li>• Search and browse code</li>
                  <li>• Manage files and branches</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Security & Privacy</h4>
                <ul className="space-y-1 text-xs">
                  <li>• Credentials stored securely in system keychain</li>
                  <li>• No data transmitted except to official APIs</li>
                  <li>• Local processing with Docker isolation</li>
                  <li>• Full control over connections and permissions</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
