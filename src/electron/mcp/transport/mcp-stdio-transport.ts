// MCP Stdio Transport - Handles stdio communication with MCP servers
import { EventEmitter } from 'events';
import { ChildProcess } from 'child_process';
import log from 'electron-log';
import {
  MCPTransport,
  MCPMessage,
  MCPResponse
} from '../core/mcp-types.js';

export class MCPStdioTransport extends EventEmitter implements MCPTransport {
  private process: ChildProcess;
  private connected = false;
  private pendingRequests = new Map<string | number, {
    resolve: (response: MCPResponse) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }>();
  private messageBuffer = '';
  private requestTimeout = 30000; // 30 seconds

  constructor(process: ChildProcess) {
    super();
    this.process = process;
  }

  /**
   * Connects the transport
   */
  async connect(): Promise<void> {
    if (this.connected) {
      return;
    }

    try {
      log.debug('Connecting MCP stdio transport');

      // Set up process event listeners
      this.setupProcessListeners();

      // Set up stdio listeners
      this.setupStdioListeners();

      this.connected = true;
      log.debug('MCP stdio transport connected');

    } catch (error) {
      log.error('Failed to connect MCP stdio transport:', error);
      throw error;
    }
  }

  /**
   * Disconnects the transport
   */
  async disconnect(): Promise<void> {
    if (!this.connected) {
      return;
    }

    try {
      log.debug('Disconnecting MCP stdio transport');

      // Reject all pending requests
      for (const [id, pending] of this.pendingRequests) {
        clearTimeout(pending.timeout);
        pending.reject(new Error('Transport disconnected'));
      }
      this.pendingRequests.clear();

      // Remove listeners
      this.removeAllListeners();

      this.connected = false;
      log.debug('MCP stdio transport disconnected');

    } catch (error) {
      log.error('Error disconnecting MCP stdio transport:', error);
      throw error;
    }
  }

  /**
   * Sends a message and waits for response
   */
  async send(message: MCPMessage): Promise<MCPResponse> {
    if (!this.connected) {
      throw new Error('Transport not connected');
    }

    if (!this.process.stdin) {
      throw new Error('Process stdin not available');
    }

    return new Promise((resolve, reject) => {
      const messageId = message.id;
      
      if (messageId === undefined) {
        // For notifications (no response expected)
        try {
          const messageStr = JSON.stringify(message) + '\n';
          this.process.stdin!.write(messageStr);
          resolve({ jsonrpc: '2.0' }); // Empty response for notifications
        } catch (error) {
          reject(error);
        }
        return;
      }

      // Set up timeout
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(messageId);
        reject(new Error(`Request timeout for message ${messageId}`));
      }, this.requestTimeout);

      // Store pending request
      this.pendingRequests.set(messageId, {
        resolve,
        reject,
        timeout
      });

      try {
        // Send message
        const messageStr = JSON.stringify(message) + '\n';
        log.debug('Sending MCP message:', messageStr.trim());
        
        this.process.stdin.write(messageStr);

      } catch (error) {
        // Clean up on send error
        clearTimeout(timeout);
        this.pendingRequests.delete(messageId);
        reject(error);
      }
    });
  }

  /**
   * Checks if transport is connected
   */
  isConnected(): boolean {
    return this.connected && !this.process.killed;
  }

  /**
   * Sets up message callback
   */
  onMessage(callback: (message: MCPMessage) => void): void {
    this.on('message', callback);
  }

  /**
   * Sets up error callback
   */
  onError(callback: (error: Error) => void): void {
    this.on('error', callback);
  }

  /**
   * Sets up disconnect callback
   */
  onDisconnect(callback: () => void): void {
    this.on('disconnect', callback);
  }

  /**
   * Sets up process event listeners
   */
  private setupProcessListeners(): void {
    this.process.on('error', (error) => {
      log.error('Process error in MCP transport:', error);
      this.handleError(error);
    });

    this.process.on('exit', (code, signal) => {
      log.warn(`Process exited in MCP transport: code=${code}, signal=${signal}`);
      this.handleDisconnect();
    });

    this.process.on('close', (code, signal) => {
      log.warn(`Process closed in MCP transport: code=${code}, signal=${signal}`);
      this.handleDisconnect();
    });
  }

  /**
   * Sets up stdio listeners
   */
  private setupStdioListeners(): void {
    if (!this.process.stdout) {
      throw new Error('Process stdout not available');
    }

    this.process.stdout.on('data', (data: Buffer) => {
      this.handleStdoutData(data);
    });

    this.process.stdout.on('error', (error) => {
      log.error('Stdout error in MCP transport:', error);
      this.handleError(error);
    });

    if (this.process.stderr) {
      this.process.stderr.on('data', (data: Buffer) => {
        const message = data.toString().trim();
        if (message) {
          log.debug('MCP server stderr:', message);
        }
      });
    }
  }

  /**
   * Handles stdout data from the process
   */
  private handleStdoutData(data: Buffer): void {
    try {
      this.messageBuffer += data.toString();

      // Process complete messages (separated by newlines)
      const lines = this.messageBuffer.split('\n');
      
      // Keep the last incomplete line in the buffer
      this.messageBuffer = lines.pop() || '';

      // Process complete lines
      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine) {
          this.processMessage(trimmedLine);
        }
      }

    } catch (error) {
      log.error('Error handling stdout data:', error);
      this.handleError(error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Processes a complete message line
   */
  private processMessage(messageStr: string): void {
    try {
      log.debug('Received MCP message:', messageStr);
      
      const message = JSON.parse(messageStr);

      // Validate JSON-RPC format
      if (!message.jsonrpc || message.jsonrpc !== '2.0') {
        log.warn('Invalid JSON-RPC message:', message);
        return;
      }

      // Handle response to our request
      if (message.id !== undefined && this.pendingRequests.has(message.id)) {
        this.handleResponse(message);
        return;
      }

      // Handle notification or request from server
      if (message.method) {
        this.handleIncomingMessage(message);
        return;
      }

      log.warn('Unhandled message:', message);

    } catch (error) {
      log.error('Error processing message:', messageStr, error);
    }
  }

  /**
   * Handles response to our request
   */
  private handleResponse(response: MCPResponse): void {
    const messageId = response.id!;
    const pending = this.pendingRequests.get(messageId);

    if (!pending) {
      log.warn('Received response for unknown request:', messageId);
      return;
    }

    // Clean up
    clearTimeout(pending.timeout);
    this.pendingRequests.delete(messageId);

    // Resolve or reject based on response
    if (response.error) {
      const error = new Error(response.error.message || 'MCP server error');
      (error as any).code = response.error.code;
      (error as any).data = response.error.data;
      pending.reject(error);
    } else {
      pending.resolve(response);
    }
  }

  /**
   * Handles incoming message from server (notification or request)
   */
  private handleIncomingMessage(message: MCPMessage): void {
    // Emit message event for listeners
    this.emit('message', message);

    // Handle specific message types
    switch (message.method) {
      case 'notifications/initialized':
        log.debug('MCP server initialized');
        break;

      case 'notifications/progress':
        log.debug('MCP progress notification:', message.params);
        break;

      case 'notifications/message':
        log.info('MCP server message:', message.params);
        break;

      default:
        log.debug('Unhandled MCP message method:', message.method);
    }
  }

  /**
   * Handles transport errors
   */
  private handleError(error: Error): void {
    log.error('MCP transport error:', error);
    this.emit('error', error);
  }

  /**
   * Handles transport disconnect
   */
  private handleDisconnect(): void {
    if (this.connected) {
      this.connected = false;
      log.debug('MCP transport disconnected');
      this.emit('disconnect');
    }
  }

  /**
   * Gets transport statistics
   */
  getStats(): {
    connected: boolean;
    pendingRequests: number;
    bufferSize: number;
    processId?: number;
  } {
    return {
      connected: this.connected,
      pendingRequests: this.pendingRequests.size,
      bufferSize: this.messageBuffer.length,
      processId: this.process.pid
    };
  }

  /**
   * Sets request timeout
   */
  setRequestTimeout(timeout: number): void {
    this.requestTimeout = timeout;
  }

  /**
   * Sends a notification (no response expected)
   */
  async sendNotification(method: string, params?: any): Promise<void> {
    const message: MCPMessage = {
      jsonrpc: '2.0',
      method,
      params
    };

    await this.send(message);
  }

  /**
   * Sends a request and waits for response
   */
  async sendRequest(method: string, params?: any): Promise<any> {
    const message: MCPMessage = {
      jsonrpc: '2.0',
      id: this.generateRequestId(),
      method,
      params
    };

    const response = await this.send(message);
    return response.result;
  }

  /**
   * Generates a unique request ID
   */
  private generateRequestId(): string {
    return `stdio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
